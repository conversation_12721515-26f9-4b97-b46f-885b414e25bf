import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import shutil

class ImageViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Selector")
        self.root.configure(bg="black")
        
        # Set fullscreen
        self.is_fullscreen = True
        self.root.attributes("-fullscreen", self.is_fullscreen)
        
        # Variables
        self.image_files = []
        self.current_index = 0
        self.destination_folder = None
        self.current_image_path = None
        self.image_label = None
        
        # Create UI elements
        self.create_ui()
        
        # Bind keyboard shortcuts
        self.bind_shortcuts()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_bar = tk.Label(self.root, textvariable=self.status_var, 
                                  bd=1, relief=tk.SUNKEN, anchor=tk.W,
                                  bg="black", fg="white")
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Start by asking for source and destination folders
        self.open_folder()
        # Ask for destination folder right after source folder is selected
        self.select_destination_folder()
    
    def create_ui(self):
        # Main frame
        self.main_frame = tk.Frame(self.root, bg="black")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Image display
        self.image_label = tk.Label(self.main_frame, bg="black")
        self.image_label.pack(fill=tk.BOTH, expand=True)
        
        # Control panel (hidden by default in fullscreen mode)
        self.control_panel = tk.Frame(self.root, bg="gray20", height=30)
        
        # Buttons
        self.prev_btn = tk.Button(self.control_panel, text="Previous", command=self.prev_image)
        self.prev_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.next_btn = tk.Button(self.control_panel, text="Next", command=self.next_image)
        self.next_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.select_dest_btn = tk.Button(self.control_panel, text="Select Destination Folder", 
                                        command=self.select_destination_folder)
        self.select_dest_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.toggle_fs_btn = tk.Button(self.control_panel, text="Toggle Fullscreen", 
                                      command=self.toggle_fullscreen)
        self.toggle_fs_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.exit_btn = tk.Button(self.control_panel, text="Exit", command=self.root.quit)
        self.exit_btn.pack(side=tk.RIGHT, padx=5, pady=5)
    
    def bind_shortcuts(self):
        self.root.bind("<Escape>", self.toggle_fullscreen)
        self.root.bind("<Right>", lambda e: self.next_image())
        self.root.bind("<Left>", lambda e: self.prev_image())
        self.root.bind("<space>", lambda e: self.move_current_image())
        self.root.bind("q", lambda e: self.root.quit())
        self.root.bind("f", lambda e: self.toggle_fullscreen())
        self.root.bind("c", lambda e: self.toggle_controls())
    
    def toggle_controls(self):
        if self.control_panel.winfo_manager():
            self.control_panel.pack_forget()
        else:
            self.control_panel.pack(side=tk.BOTTOM, fill=tk.X)
    
    def toggle_fullscreen(self, event=None):
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes("-fullscreen", self.is_fullscreen)
        if not self.is_fullscreen:
            self.control_panel.pack(side=tk.BOTTOM, fill=tk.X)
        else:
            self.control_panel.pack_forget()
        return "break"
    
    def open_folder(self):
        folder_path = filedialog.askdirectory(title="Select Folder with Images")
        if not folder_path:
            messagebox.showerror("Error", "No folder selected. Exiting application.")
            self.root.quit()
            return
        
        # Get all image files from the folder
        self.image_files = []
        valid_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff')
        
        for file in os.listdir(folder_path):
            if file.lower().endswith(valid_extensions):
                self.image_files.append(os.path.join(folder_path, file))
        
        if not self.image_files:
            messagebox.showerror("Error", "No image files found in the selected folder.")
            self.root.quit()
            return
        
        # Sort files by name
        self.image_files.sort()
        self.current_index = 0
        self.load_image()
    
    def select_destination_folder(self):
        folder_path = filedialog.askdirectory(title="Select Destination Folder")
        if folder_path:
            self.destination_folder = folder_path
            self.status_var.set(f"Destination folder: {self.destination_folder}")
    
    def load_image(self):
        if not self.image_files or self.current_index >= len(self.image_files):
            messagebox.showinfo("End of Images", "No more images to display.")
            return
        
        try:
            self.current_image_path = self.image_files[self.current_index]
            image = Image.open(self.current_image_path)
            
            # Resize image to fit the screen while maintaining aspect ratio
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight() - 50  # Leave space for status bar
            
            img_width, img_height = image.size
            ratio = min(screen_width/img_width, screen_height/img_height)
            new_width = int(img_width * ratio)
            new_height = int(img_height * ratio)
            
            image = image.resize((new_width, new_height), Image.LANCZOS)
            
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)
            
            # Update image display
            self.image_label.config(image=photo)
            self.image_label.image = photo  # Keep a reference
            
            # Update status
            filename = os.path.basename(self.current_image_path)
            self.status_var.set(f"Image {self.current_index + 1}/{len(self.image_files)}: {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
            self.next_image()  # Skip problematic image
    
    def next_image(self):
        if self.current_index < len(self.image_files) - 1:
            self.current_index += 1
            self.load_image()
    
    def prev_image(self):
        if self.current_index > 0:
            self.current_index -= 1
            self.load_image()
    
    def move_current_image(self):
        if not self.destination_folder:
            messagebox.showwarning("Warning", "Please select a destination folder first.")
            self.select_destination_folder()
            return
        
        if not self.current_image_path or not os.path.exists(self.current_image_path):
            messagebox.showerror("Error", "No valid image to move.")
            return
        
        try:
            # Get the filename
            filename = os.path.basename(self.current_image_path)
            destination_path = os.path.join(self.destination_folder, filename)
            
            # Check if file already exists in destination
            if os.path.exists(destination_path):
                # Add a number to the filename to make it unique
                name, ext = os.path.splitext(filename)
                counter = 1
                while os.path.exists(os.path.join(self.destination_folder, f"{name}_{counter}{ext}")):
                    counter += 1
                destination_path = os.path.join(self.destination_folder, f"{name}_{counter}{ext}")
            
            # Move the file
            shutil.move(self.current_image_path, destination_path)
            
            # Remove from list and update
            self.image_files.pop(self.current_index)
            
            # If we've moved the last image, go to the previous one
            if self.current_index >= len(self.image_files) and self.current_index > 0:
                self.current_index -= 1
            
            # Load next image (or current index if we removed the last one)
            if self.image_files:
                self.load_image()
            else:
                messagebox.showinfo("Complete", "All images have been processed.")
                self.root.quit()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to move image: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ImageViewer(root)
    root.mainloop()